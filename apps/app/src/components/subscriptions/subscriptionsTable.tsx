"use client"

import React, { use<PERSON><PERSON>back, useEffect, useState } from "react"
import { Edit, RotateCcw, Search } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { But<PERSON> } from "@nextui-org/button"
import { Chip } from "@nextui-org/chip"
import { Input } from "@nextui-org/input"
import { Pagination } from "@nextui-org/pagination"
import { Select, SelectItem } from "@nextui-org/select"
import { Spinner } from "@nextui-org/spinner"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"
import { Tooltip } from "@nextui-org/tooltip"
import { Prisma, SubscriptionStatus } from "@prisma/client"

import UpdateSubscriptionModal from "./updateSubscriptionModal"

type SubscriptionWithUserAndPlan = Prisma.SubscriptionGetPayload<{
  include: {
    user: {
      select: {
        id: true
        name: true
        email: true
      }
    }
    plan: true
  }
}>

interface SubscriptionsTableProps {
  initialSubscriptions: SubscriptionWithUserAndPlan[]
  initialPagination: {
    page: number
    pageSize: number
    totalCount: number
    totalPages: number
  }
}

export const SubscriptionsTable = ({ initialSubscriptions, initialPagination }: SubscriptionsTableProps) => {
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithUserAndPlan[]>(initialSubscriptions)
  const [pagination, setPagination] = useState(initialPagination)
  const [page, setPage] = useState(initialPagination.page)
  const [pageSize, setPageSize] = useState(initialPagination.pageSize)
  const [selectedSubscription, setSelectedSubscription] = useState<SubscriptionWithUserAndPlan | null>(null)
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false)
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [prevPage, setPrevPage] = useState(initialPagination.page)
  const [prevPageSize, setPrevPageSize] = useState(initialPagination.pageSize)
  const [filterValue, setFilterValue] = useState("")
  const [filterColumn, setFilterColumn] = useState("user")

  const subscriptionsQuery = trpc.subscription.getAllForAdmin.useQuery(
    { page, pageSize },
    { enabled: false, initialData: { data: initialSubscriptions, pagination: initialPagination } }
  )

  const loadData = useCallback(async () => {
    setIsLoading(true)
    try {
      const result = await subscriptionsQuery.refetch()
      if (result.data) {
        setSubscriptions(result.data.data)
        setPagination(result.data.pagination)
      }
    } finally {
      setIsLoading(false)
    }
  }, [subscriptionsQuery])

  const handleSubscriptionsMutated = useCallback(async () => {
    await loadData()
  }, [loadData])

  useEffect(() => {
    const pageChanged = page !== prevPage
    const pageSizeChanged = pageSize !== prevPageSize

    if (pageChanged || pageSizeChanged) {
      loadData()
      setPrevPage(page)
      setPrevPageSize(pageSize)
    }
  }, [page, pageSize, prevPage, prevPageSize, loadData])

  const cancelSubscription = trpc.subscription.cancelByAdmin.useMutation({
    onSuccess: () => {
      toast.success("Abonnement annulé avec succès")
      handleSubscriptionsMutated()
      setIsCancelModalOpen(false)
    },
    onError: (error) => {
      toast.error(`Erreur lors de l'annulation: ${error.message}`)
    },
  })

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = Number(e.target.value)
    setPageSize(newSize)
    setPage(1)
  }

  const handleEditClick = (subscription: SubscriptionWithUserAndPlan) => {
    setSelectedSubscription(subscription)
    setIsUpdateModalOpen(true)
  }

  const handleCancelClick = (subscription: SubscriptionWithUserAndPlan) => {
    setSelectedSubscription(subscription)
    setIsCancelModalOpen(true)
  }

  const confirmCancel = () => {
    if (selectedSubscription) {
      cancelSubscription.mutate(selectedSubscription.id)
    }
  }

  const formatDate = (dateString: Date) => {
    return dateString.toLocaleDateString("fr-FR")
  }

  const getStatusColor = (status: SubscriptionStatus) => {
    switch (status) {
      case "ACTIVE":
        return "success"
      case "FAILED":
        return "danger"
      case "CANCELED":
      case "PENDING":
        return "warning"
      default:
        return "default"
    }
  }

  const getStatusLabel = (status: SubscriptionStatus) => {
    switch (status) {
      case "ACTIVE":
        return "Actif"
      case "CANCELED":
        return "Annulé"
      case "EXPIRED":
        return "Expiré"
      case "PENDING":
        return "En attente"
      case "FAILED":
        return "Échec"
      default:
        return status
    }
  }

  // Filtrage des abonnements
  const filteredSubscriptions = React.useMemo(() => {
    if (!filterValue) return subscriptions

    return subscriptions.filter((subscription) => {
      switch (filterColumn) {
        case "user":
          return (
            subscription.user.name?.toLowerCase().includes(filterValue.toLowerCase()) ||
            subscription.user.email?.toLowerCase().includes(filterValue.toLowerCase())
          )
        case "plan":
          return subscription.plan.name.toLowerCase().includes(filterValue.toLowerCase())
        case "status":
          return getStatusLabel(subscription.status).toLowerCase().includes(filterValue.toLowerCase())
        default:
          return true
      }
    })
  }, [subscriptions, filterValue, filterColumn])

  return (
    <div className="space-y-4">
      <div className="flex justify-between">
        <h3 className="text-xl font-semibold">Liste des abonnements ({pagination.totalCount})</h3>
      </div>

      {/* Filtres */}
      <div className="mb-4 flex gap-4">
        <Input
          isClearable
          className="w-full sm:min-w-[200px] sm:max-w-[44%]"
          placeholder="Rechercher..."
          startContent={<Search className="text-default-300" size={18} />}
          value={filterValue}
          onClear={() => setFilterValue("")}
          onValueChange={setFilterValue}
        />
        <Select
          className="w-full sm:min-w-[150px] sm:max-w-[200px]"
          selectedKeys={[filterColumn]}
          onChange={(e) => setFilterColumn(e.target.value)}
          aria-label="Filtrer par"
        >
          <SelectItem key="user" value="user">
            Utilisateur
          </SelectItem>
          <SelectItem key="plan" value="plan">
            Plan
          </SelectItem>
          <SelectItem key="status" value="status">
            Statut
          </SelectItem>
        </Select>
      </div>

      <Table
        aria-label="Table des abonnements"
        bottomContent={
          <div className="flex w-full items-center justify-between">
            <Select
              className="w-28"
              size="sm"
              label="Lignes"
              value={pageSize.toString()}
              onChange={handlePageSizeChange}
            >
              <SelectItem key="10" value="10">
                10
              </SelectItem>
              <SelectItem key="15" value="15">
                15
              </SelectItem>
              <SelectItem key="25" value="25">
                25
              </SelectItem>
              <SelectItem key="50" value="50">
                50
              </SelectItem>
            </Select>
            <Pagination
              showControls
              showShadow
              color="primary"
              page={page}
              total={pagination.totalPages}
              onChange={handlePageChange}
            />
          </div>
        }
      >
        <TableHeader>
          <TableColumn>UTILISATEUR</TableColumn>
          <TableColumn>PLAN</TableColumn>
          <TableColumn>STATUT</TableColumn>
          <TableColumn>PÉRIODE</TableColumn>
          <TableColumn>DATE DE DÉBUT</TableColumn>
          <TableColumn>DATE DE FIN</TableColumn>
          <TableColumn>ACTIONS</TableColumn>
        </TableHeader>
        <TableBody
          items={filteredSubscriptions}
          loadingState={isLoading ? "loading" : "idle"}
          loadingContent={<Spinner label="Chargement..." />}
          emptyContent="Aucun abonnement trouvé"
        >
          {(subscription) => (
            <TableRow key={subscription.id}>
              <TableCell>
                <div>
                  <p className="font-medium">{subscription.user.name}</p>
                  <p className="text-xs text-gray-500">{subscription.user.email}</p>
                </div>
              </TableCell>
              <TableCell>{subscription.plan.name}</TableCell>
              <TableCell>
                <Chip color={getStatusColor(subscription.status)} variant="flat" size="sm">
                  {getStatusLabel(subscription.status)}
                </Chip>
              </TableCell>
              <TableCell>{subscription.billingPeriod === "MONTHLY" ? "Mensuel" : "Annuel"}</TableCell>
              <TableCell>{formatDate(subscription.startDate)}</TableCell>
              <TableCell>{formatDate(subscription.endDate)}</TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <Tooltip content="Modifier">
                    <Button isIconOnly size="sm" variant="light" onPress={() => handleEditClick(subscription)}>
                      <Edit size={16} />
                    </Button>
                  </Tooltip>
                  {subscription.status === "ACTIVE" && (
                    <Tooltip content="Annuler">
                      <Button
                        isIconOnly
                        size="sm"
                        variant="light"
                        color="danger"
                        onPress={() => handleCancelClick(subscription)}
                      >
                        <RotateCcw size={16} />
                      </Button>
                    </Tooltip>
                  )}
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Modal de mise à jour */}
      {selectedSubscription && (
        <UpdateSubscriptionModal
          isOpen={isUpdateModalOpen}
          onClose={() => setIsUpdateModalOpen(false)}
          subscription={selectedSubscription}
          onSuccess={handleSubscriptionsMutated}
        />
      )}

      {/* Modal de confirmation d'annulation */}
      <div className={`fixed inset-0 z-50 flex items-center justify-center ${isCancelModalOpen ? "" : "hidden"}`}>
        <div className="fixed inset-0 bg-black/50" onClick={() => setIsCancelModalOpen(false)}></div>
        <div className="relative z-10 w-full max-w-md rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800">
          <h3 className="mb-4 text-xl font-bold">Confirmer l&apos;annulation</h3>
          <p>
            Êtes-vous sûr de vouloir annuler l&apos;abonnement de{" "}
            <span className="font-semibold">{selectedSubscription?.user.name}</span> au plan{" "}
            <span className="font-semibold">{selectedSubscription?.plan.name}</span> ?
          </p>
          <div className="mt-6 flex justify-end gap-2">
            <Button variant="flat" onPress={() => setIsCancelModalOpen(false)}>
              Annuler
            </Button>
            <Button color="danger" onPress={confirmCancel} isLoading={cancelSubscription.isPending}>
              Confirmer
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
