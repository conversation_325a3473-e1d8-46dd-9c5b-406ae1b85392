import { z } from "zod"

import { prisma } from "@/lib/prisma"
import { authenticatedProcedure, modoAuthenticatedProcedure, router } from "@/lib/server/trpc"
import { checkUserMangopaySetup, createSubscription } from "@/lib/subscription"
import { BillingPeriod, SubscriptionStatus } from "@prisma/client"
import { TRPCError } from "@trpc/server"

export const subscriptionRouter = router({
  create: authenticatedProcedure
    .input(
      z.object({
        planId: z.number(),
        billingPeriod: z.enum([BillingPeriod.MONTHLY, BillingPeriod.ANNUAL]),
        cardId: z.string(),
        browserInfos: z.object({
          AcceptHeader: z.string(),
          JavaEnabled: z.boolean(),
          JavascriptEnabled: z.boolean(),
          Language: z.string(),
          ColorDepth: z.number(),
          ScreenHeight: z.number(),
          ScreenWidth: z.number(),
          TimeZoneOffset: z.number(),
          UserAgent: z.string(),
        }),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { planId, billingPeriod, cardId, browserInfos } = input

      if (!ctx?.session?.user.id) {
        throw new Error("User not authenticated")
      }

      const IP = ctx.req?.headers.get("x-forwarded-for") ?? ""

      // Call the updated createSubscription function
      const result = await createSubscription({
        userId: ctx.session.user.id,
        planId,
        billingPeriod,
        browserInfos,
        IP,
        mangopayCardId: cardId,
      })

      // Return the necessary data, including the redirectUrl
      return {
        subscription: result.subscription,
        payment: result.payment,
        redirectUrl: result.redirectUrl,
        returnUrl: result.returnUrl,
      }
    }),

  checkSetup: authenticatedProcedure.query(async ({ ctx }) => {
    if (!ctx?.session?.user.id) {
      throw new Error("User not authenticated")
    }
    return await checkUserMangopaySetup(ctx.session.user.id)
  }),

  getAllForAdmin: modoAuthenticatedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        pageSize: z.number().min(1).max(50).default(15),
      })
    )
    .query(async ({ input }) => {
      const { page, pageSize } = input
      const skip = (page - 1) * pageSize

      const subscriptions = await prisma.subscription.findMany({
        skip,
        take: pageSize,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          plan: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      })

      const totalCount = await prisma.subscription.count()

      return {
        data: subscriptions,
        pagination: {
          page,
          pageSize,
          totalCount,
          totalPages: Math.ceil(totalCount / pageSize),
        },
      }
    }),

  cancelByAdmin: modoAuthenticatedProcedure.input(z.string()).mutation(async ({ input }) => {
    const subscriptionId = input

    // Vérifier si l'abonnement existe
    const subscription = await prisma.subscription.findUnique({
      where: { id: subscriptionId },
    })

    if (!subscription) {
      throw new Error("Abonnement non trouvé")
    }

    // Mettre à jour le statut de l'abonnement
    const updatedSubscription = await prisma.subscription.update({
      where: { id: subscriptionId },
      data: {
        status: "CANCELED",
        canceledAt: new Date(),
      },
    })

    return updatedSubscription
  }),

  updateByAdmin: modoAuthenticatedProcedure
    .input(
      z.object({
        id: z.string(),
        planId: z.number().optional(),
        status: z
          .enum([
            SubscriptionStatus.ACTIVE,
            SubscriptionStatus.CANCELED,
            SubscriptionStatus.EXPIRED,
            SubscriptionStatus.FAILED,
            SubscriptionStatus.PENDING,
          ])
          .optional(),
        billingPeriod: z.enum([BillingPeriod.MONTHLY, BillingPeriod.ANNUAL]).optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        canceledAt: z.date().nullable().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const { id, ...updateData } = input

      // Vérifier si l'abonnement existe
      const subscription = await prisma.subscription.findUnique({
        where: { id },
        include: { plan: true },
      })

      if (!subscription) {
        throw new Error("Abonnement non trouvé")
      }

      // Si le statut passe à CANCELED, définir canceledAt à la date actuelle
      // sauf si canceledAt est explicitement fourni
      if (updateData.status === "CANCELED" && updateData.canceledAt === undefined) {
        updateData.canceledAt = new Date()
      }

      // Si le statut passe à ACTIVE depuis CANCELED, réinitialiser canceledAt
      if (updateData.status === "ACTIVE" && subscription.status === "CANCELED") {
        updateData.canceledAt = null
      }

      // Mettre à jour l'abonnement
      const updatedSubscription = await prisma.subscription.update({
        where: { id },
        data: updateData,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          plan: true,
        },
      })

      return updatedSubscription
    }),

  getUserActivePlan: authenticatedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session?.user.id

    if (!userId) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "Utilisateur non authentifié" })
    }

    // Récupérer l'abonnement actif de l'utilisateur
    const activeSubscription = await prisma.subscription.findFirst({
      where: {
        userId,
        status: "ACTIVE",
      },
      include: {
        plan: true,
      },
    })

    if (!activeSubscription) {
      return null
    }

    // Récupérer les restrictions du plan
    const planRestrictions = await prisma.planRestriction.findMany({
      where: {
        planId: activeSubscription.planId,
      },
    })

    // Convertir les features de JSON string à objets
    const features = activeSubscription.plan.features.map((feature) => {
      try {
        return JSON.parse(feature)
      } catch (e) {
        return { text: feature, included: true }
      }
    })

    return {
      ...activeSubscription.plan,
      features,
      restrictions: planRestrictions,
    }
  }),
})
