import { router } from "../lib/server/trpc"

import { agentRouter } from "./agent/_router"
import { authRouter } from "./auth/_router"
import { badgeRouter } from "./badge/_router"
import { cardRouter } from "./card/_router"
import { categoryRouter } from "./category/_router"
import { meRouter } from "./me/_router"
import { planRouter } from "./plan/_router"
import { promptRouter } from "./prompt/_router"
import { skillRouter } from "./skill/_router"
import { subscriptionRouter } from "./subscription/_router"
import { messageRouter } from "./support-message/_router"
import { ticketRouter } from "./tickets/_router"
import { uploadRouter } from "./upload/_router"
import { userRouter } from "./users/_router"

export const appRouter = router({
  agent: agentRouter,
  auth: authRouter,
  badge: badgeRouter,
  card: cardRouter,
  category: categoryRouter,
  me: meRouter,
  plan: planRouter,
  prompt: promptRouter,
  skill: skillRouter,
  subscription: subscriptionRouter,
  message: messageRouter,
  ticket: ticketRouter,
  upload: uploadRouter,
  user: userRouter,
})

export type AppRouter = typeof appRouter
